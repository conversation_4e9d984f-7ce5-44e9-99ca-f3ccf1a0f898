{"description": "田径训练管理小程序", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wxf8bcfac585312cbe", "projectname": "track-field-training", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}, {"type": "file", "value": ".giti<PERSON>re"}, {"type": "file", "value": "README.md"}, {"type": "folder", "value": "docs"}, {"type": "folder", "value": "tests"}], "include": []}, "editorSetting": {}, "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"name": "首页", "pathName": "pages/index/index", "query": "", "scene": null}, {"name": "登录页", "pathName": "pages/login/login", "query": "", "scene": null}, {"name": "运动员列表", "pathName": "pages/athlete/list/list", "query": "", "scene": null}, {"name": "运动员详情", "pathName": "pages/athlete/detail/detail", "query": "id=1", "scene": null}, {"name": "训练计划", "pathName": "pages/training/plan/plan", "query": "", "scene": null}]}}}