/* styles/theme.wxss */

/* 主题色彩变量 */
page {
  /* 主色调 */
  --primary-color: #2E86AB;
  --primary-light: #4A9BC7;
  --primary-dark: #1E5F7A;
  --primary-gradient: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  
  /* 辅助色 */
  --secondary-color: #A23B72;
  --secondary-light: #B85A8A;
  --secondary-dark: #7A2B55;
  
  /* 功能色 */
  --success-color: #67C23A;
  --success-light: #85CE61;
  --success-dark: #529B2E;
  
  --warning-color: #E6A23C;
  --warning-light: #ELBF63;
  --warning-dark: #B88230;
  
  --danger-color: #F56C6C;
  --danger-light: #F78989;
  --danger-dark: #DD6161;
  
  --info-color: #909399;
  --info-light: #A6A9AD;
  --info-dark: #82848A;
  
  /* 中性色 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;
  --text-disabled: #C0C4CC;
  
  /* 边框色 */
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;
  
  /* 背景色 */
  --bg-white: #FFFFFF;
  --bg-page: #F5F7FA;
  --bg-light: #FAFBFC;
  --bg-lighter: #F8F9FA;
  --bg-dark: #2C3E50;
  --bg-darker: #1A252F;
  
  /* 阴影 */
  --shadow-light: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  --shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  --shadow-dark: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --border-radius-small: 4rpx;
  --border-radius-base: 8rpx;
  --border-radius-large: 12rpx;
  --border-radius-round: 20rpx;
  --border-radius-circle: 50%;
  
  /* 字体大小 */
  --font-size-extra-small: 20rpx;
  --font-size-small: 24rpx;
  --font-size-base: 28rpx;
  --font-size-medium: 32rpx;
  --font-size-large: 36rpx;
  --font-size-extra-large: 40rpx;
  
  /* 行高 */
  --line-height-base: 1.4;
  --line-height-medium: 1.5;
  --line-height-large: 1.6;
  
  /* 间距 */
  --spacing-mini: 8rpx;
  --spacing-small: 12rpx;
  --spacing-base: 16rpx;
  --spacing-medium: 20rpx;
  --spacing-large: 24rpx;
  --spacing-extra-large: 32rpx;
  
  /* 动画时间 */
  --transition-base: 0.3s;
  --transition-fast: 0.2s;
  --transition-slow: 0.5s;
}

/* 主题色彩类 */
.text-primary {
  color: var(--primary-color);
}

.text-primary-light {
  color: var(--primary-light);
}

.text-primary-dark {
  color: var(--primary-dark);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

.text-regular {
  color: var(--text-regular);
}

.text-secondary-text {
  color: var(--text-secondary);
}

.text-placeholder {
  color: var(--text-placeholder);
}

.text-disabled {
  color: var(--text-disabled);
}

/* 背景色类 */
.bg-primary {
  background-color: var(--primary-color);
}

.bg-primary-light {
  background-color: var(--primary-light);
}

.bg-primary-dark {
  background-color: var(--primary-dark);
}

.bg-secondary {
  background-color: var(--secondary-color);
}

.bg-success {
  background-color: var(--success-color);
}

.bg-warning {
  background-color: var(--warning-color);
}

.bg-danger {
  background-color: var(--danger-color);
}

.bg-info {
  background-color: var(--info-color);
}

.bg-white {
  background-color: var(--bg-white);
}

.bg-page {
  background-color: var(--bg-page);
}

.bg-light {
  background-color: var(--bg-light);
}

.bg-lighter {
  background-color: var(--bg-lighter);
}

.bg-gradient {
  background: var(--primary-gradient);
}

/* 边框色类 */
.border-base {
  border-color: var(--border-base);
}

.border-light {
  border-color: var(--border-light);
}

.border-lighter {
  border-color: var(--border-lighter);
}

.border-extra-light {
  border-color: var(--border-extra-light);
}

.border-primary {
  border-color: var(--primary-color);
}

.border-success {
  border-color: var(--success-color);
}

.border-warning {
  border-color: var(--warning-color);
}

.border-danger {
  border-color: var(--danger-color);
}

/* 运动项目主题色 */
.track-sprint {
  --theme-color: #FF6B6B;
  --theme-light: #FF8E8E;
  --theme-dark: #E55555;
}

.track-middle {
  --theme-color: #4ECDC4;
  --theme-light: #6ED5CE;
  --theme-dark: #3BB5AD;
}

.track-long {
  --theme-color: #45B7D1;
  --theme-light: #6BC5D8;
  --theme-dark: #3A9BC1;
}

.track-hurdle {
  --theme-color: #96CEB4;
  --theme-light: #A8D4C1;
  --theme-dark: #7FB89D;
}

.track-relay {
  --theme-color: #FFEAA7;
  --theme-light: #FFF0C4;
  --theme-dark: #F5D76E;
}

.field-jump {
  --theme-color: #DDA0DD;
  --theme-light: #E6B3E6;
  --theme-dark: #C78BC7;
}

.field-throw {
  --theme-color: #F39C12;
  --theme-light: #F5B041;
  --theme-dark: #D68910;
}

.field-combined {
  --theme-color: #9B59B6;
  --theme-light: #AF7AC5;
  --theme-dark: #8E44AD;
}

/* 性能等级主题色 */
.level-beginner {
  --level-color: #95A5A6;
  --level-bg: #ECF0F1;
}

.level-intermediate {
  --level-color: #3498DB;
  --level-bg: #EBF5FB;
}

.level-advanced {
  --level-color: #E67E22;
  --level-bg: #FDF2E9;
}

.level-elite {
  --level-color: #E74C3C;
  --level-bg: #FDEDEC;
}

.level-professional {
  --level-color: #8E44AD;
  --level-bg: #F4ECF7;
}

/* 训练强度主题色 */
.intensity-low {
  --intensity-color: #27AE60;
  --intensity-bg: #E8F8F5;
}

.intensity-medium {
  --intensity-color: #F39C12;
  --intensity-bg: #FEF9E7;
}

.intensity-high {
  --intensity-color: #E74C3C;
  --intensity-bg: #FDEDEC;
}

.intensity-max {
  --intensity-color: #8E44AD;
  --intensity-bg: #F4ECF7;
}

/* 状态主题色 */
.status-active {
  --status-color: var(--success-color);
  --status-bg: #F0F9FF;
}

.status-inactive {
  --status-color: var(--info-color);
  --status-bg: #F8F9FA;
}

.status-pending {
  --status-color: var(--warning-color);
  --status-bg: #FEF9E7;
}

.status-completed {
  --status-color: var(--success-color);
  --status-bg: #E8F8F5;
}

.status-cancelled {
  --status-color: var(--danger-color);
  --status-bg: #FDEDEC;
}

/* 深色主题 */
.theme-dark {
  --text-primary: #FFFFFF;
  --text-regular: #E5E5E5;
  --text-secondary: #B3B3B3;
  --text-placeholder: #808080;
  --text-disabled: #666666;
  
  --bg-white: #1A1A1A;
  --bg-page: #121212;
  --bg-light: #2A2A2A;
  --bg-lighter: #333333;
  
  --border-base: #404040;
  --border-light: #333333;
  --border-lighter: #2A2A2A;
  --border-extra-light: #1F1F1F;
}

/* 高对比度主题 */
.theme-high-contrast {
  --text-primary: #000000;
  --text-regular: #000000;
  --text-secondary: #333333;
  
  --bg-white: #FFFFFF;
  --bg-page: #FFFFFF;
  --bg-light: #F0F0F0;
  
  --border-base: #000000;
  --border-light: #333333;
  --border-lighter: #666666;
  
  --primary-color: #0000FF;
  --success-color: #008000;
  --warning-color: #FF8000;
  --danger-color: #FF0000;
}

/* 护眼主题 */
.theme-eye-care {
  --bg-white: #F7F3E9;
  --bg-page: #F0EBD8;
  --bg-light: #F5F0E1;
  --bg-lighter: #F9F6ED;
  
  --text-primary: #3E2723;
  --text-regular: #5D4037;
  --text-secondary: #795548;
  
  --primary-color: #6D4C41;
  --primary-light: #8D6E63;
  --primary-dark: #5D4037;
}
