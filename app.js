// app.js
import { auth } from './utils/auth.js'
import { storage } from './utils/storage.js'
import { api } from './utils/api.js'

App({
  globalData: {
    userInfo: null,
    userRole: null, // 'athlete', 'coach', 'admin'
    isLoggedIn: false,
    systemInfo: null,
    networkType: 'unknown',
    isOnline: true,
    version: '1.0.0'
  },

  onLaunch(options) {
    console.log('App Launch', options)
    this.initApp()
  },

  onShow(options) {
    console.log('App Show', options)
    this.checkNetworkStatus()
  },

  onHide() {
    console.log('App Hide')
  },

  onError(msg) {
    console.error('App Error', msg)
    // 错误上报
    this.reportError(msg)
  },

  onPageNotFound(res) {
    console.log('Page Not Found', res)
    wx.redirectTo({
      url: '/pages/index/index'
    })
  },

  onUnhandledRejection(res) {
    console.error('Unhandled Promise Rejection', res)
    this.reportError(res.reason)
  },

  // 应用初始化
  async initApp() {
    try {
      // 获取系统信息
      await this.getSystemInfo()
      
      // 检查网络状态
      await this.checkNetworkStatus()
      
      // 初始化云开发
      if (wx.cloud) {
        wx.cloud.init({
          env: 'track-field-training', // 云开发环境ID
          traceUser: true
        })
      }
      
      // 检查登录状态
      await this.checkLoginStatus()
      
      // 初始化本地存储
      await storage.init()
      
      console.log('App initialized successfully')
    } catch (error) {
      console.error('App initialization failed:', error)
      this.showError('应用初始化失败，请重启小程序')
    }
  },

  // 获取系统信息
  getSystemInfo() {
    return new Promise((resolve) => {
      wx.getSystemInfo({
        success: (res) => {
          this.globalData.systemInfo = res
          console.log('System Info:', res)
          resolve(res)
        },
        fail: (error) => {
          console.error('Get system info failed:', error)
          resolve(null)
        }
      })
    })
  },

  // 检查网络状态
  checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          this.globalData.networkType = res.networkType
          this.globalData.isOnline = res.networkType !== 'none'
          
          // 监听网络状态变化
          wx.onNetworkStatusChange((result) => {
            this.globalData.networkType = result.networkType
            this.globalData.isOnline = result.isConnected
            
            if (result.isConnected) {
              console.log('Network connected')
              // 网络恢复时同步数据
              this.syncDataWhenOnline()
            } else {
              console.log('Network disconnected')
              this.showToast('网络连接已断开，将使用离线模式')
            }
          })
          
          resolve(res)
        },
        fail: (error) => {
          console.error('Get network type failed:', error)
          resolve(null)
        }
      })
    })
  },

  // 检查登录状态
  async checkLoginStatus() {
    try {
      const loginInfo = await storage.get('loginInfo')
      if (loginInfo && loginInfo.token) {
        // 验证token是否有效
        const isValid = await auth.validateToken(loginInfo.token)
        if (isValid) {
          this.globalData.userInfo = loginInfo.userInfo
          this.globalData.userRole = loginInfo.userRole
          this.globalData.isLoggedIn = true
          console.log('User already logged in:', loginInfo.userInfo)
        } else {
          // token无效，清除登录信息
          await this.logout()
        }
      }
    } catch (error) {
      console.error('Check login status failed:', error)
    }
  },

  // 用户登录
  async login(userInfo, userRole) {
    try {
      this.globalData.userInfo = userInfo
      this.globalData.userRole = userRole
      this.globalData.isLoggedIn = true
      
      // 保存登录信息到本地
      await storage.set('loginInfo', {
        userInfo,
        userRole,
        token: userInfo.token,
        loginTime: Date.now()
      })
      
      console.log('User logged in successfully:', userInfo)
      return true
    } catch (error) {
      console.error('Login failed:', error)
      return false
    }
  },

  // 用户登出
  async logout() {
    try {
      this.globalData.userInfo = null
      this.globalData.userRole = null
      this.globalData.isLoggedIn = false
      
      // 清除本地登录信息
      await storage.remove('loginInfo')
      
      // 跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })
      
      console.log('User logged out successfully')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  },

  // 网络恢复时同步数据
  async syncDataWhenOnline() {
    try {
      if (this.globalData.isLoggedIn) {
        // 同步待上传的数据
        const { sync } = await import('./utils/sync.js')
        await sync.syncPendingData()
      }
    } catch (error) {
      console.error('Sync data when online failed:', error)
    }
  },

  // 错误上报
  reportError(error) {
    try {
      // 上报错误到服务器
      api.reportError({
        error: error.toString(),
        userInfo: this.globalData.userInfo,
        systemInfo: this.globalData.systemInfo,
        timestamp: Date.now()
      }).catch(err => {
        console.error('Report error failed:', err)
      })
    } catch (e) {
      console.error('Report error exception:', e)
    }
  },

  // 显示错误信息
  showError(message) {
    wx.showModal({
      title: '错误',
      content: message,
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 显示提示信息
  showToast(message, icon = 'none') {
    wx.showToast({
      title: message,
      icon: icon,
      duration: 2000
    })
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo
  },

  // 获取用户角色
  getUserRole() {
    return this.globalData.userRole
  },

  // 检查是否已登录
  isLoggedIn() {
    return this.globalData.isLoggedIn
  },

  // 检查网络状态
  isOnline() {
    return this.globalData.isOnline
  }
})
