// pages/index/index.js
import { auth } from '../../utils/auth.js'
import { formatTime, getRelativeTime } from '../../utils/util.js'
import { athleteAPI, trainingAPI, competitionAPI } from '../../utils/api.js'

Page({
  data: {
    userInfo: null,
    greeting: '',
    roleText: '',
    loading: false,
    statsData: [],
    menuItems: [],
    recentActivities: [],
    todayTraining: null,
    quickActions: [],
    notices: []
  },

  onLoad(options) {
    console.log('Index page loaded')
    this.initPage()
  },

  onShow() {
    this.refreshUserInfo()
    this.loadPageData()
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true })

      // 设置问候语
      this.setGreeting()

      // 初始化菜单
      this.initMenuItems()

      // 初始化快捷操作
      this.initQuickActions()

    } catch (error) {
      console.error('Init page failed:', error)
      this.showError('页面初始化失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 刷新用户信息
  refreshUserInfo() {
    const app = getApp()
    if (app && app.getUserInfo) {
      const userInfo = app.getUserInfo()
      const userRole = app.getUserRole()

      if (userInfo) {
        this.setData({
          userInfo,
          roleText: this.getRoleText(userRole)
        })
      }
    }
  },

  // 加载页面数据
  async loadPageData() {
    try {
      this.setData({ loading: true })
      // 模拟数据加载
      await this.loadMockData()
    } catch (error) {
      console.error('Load page data failed:', error)
      this.showError('数据加载失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载模拟数据
  async loadMockData() {
    const statsData = [
      { key: 'athletes', value: '25', label: '运动员', icon: 'icon-user', iconClass: 'stat-icon-primary' },
      { key: 'trainings', value: '12', label: '训练计划', icon: 'icon-training', iconClass: 'stat-icon-success' },
      { key: 'competitions', value: '5', label: '比赛', icon: 'icon-trophy', iconClass: 'stat-icon-warning' },
      { key: 'records', value: '156', label: '记录', icon: 'icon-chart', iconClass: 'stat-icon-info' }
    ]

    this.setData({ statsData })
  },

  // 设置问候语
  setGreeting() {
    const hour = new Date().getHours()
    let greeting = ''

    if (hour < 6) {
      greeting = '夜深了'
    } else if (hour < 9) {
      greeting = '早上好'
    } else if (hour < 12) {
      greeting = '上午好'
    } else if (hour < 14) {
      greeting = '中午好'
    } else if (hour < 18) {
      greeting = '下午好'
    } else if (hour < 22) {
      greeting = '晚上好'
    } else {
      greeting = '夜深了'
    }

    this.setData({ greeting })
  },

  // 获取角色文本
  getRoleText(role) {
    const roleMap = {
      admin: '管理员',
      coach: '教练',
      athlete: '运动员'
    }
    return roleMap[role] || '用户'
  },

  // 初始化菜单项
  initMenuItems() {
    const menuItems = [
      { id: 'athletes', label: '运动员管理', icon: '👤', iconClass: 'menu-icon-athlete', url: '/pages/athlete/list/list', requireAuth: false },
      { id: 'training', label: '训练管理', icon: '🏃', iconClass: 'menu-icon-training', url: '/pages/training/plan/plan', requireAuth: false },
      { id: 'competition', label: '比赛管理', icon: '🏆', iconClass: 'menu-icon-competition', url: '/pages/competition/list/list', requireAuth: false },
      { id: 'profile', label: '个人中心', icon: '⚙️', iconClass: 'menu-icon-profile', url: '/pages/profile/profile', requireAuth: false }
    ]

    this.setData({ menuItems })
  },

  // 初始化快捷操作
  initQuickActions() {
    const quickActions = [
      { id: 'add-athlete', label: '添加运动员', icon: '➕', iconClass: 'action-icon-add', action: 'addAthlete' },
      { id: 'create-training', label: '创建训练', icon: '📝', iconClass: 'action-icon-training', action: 'createTraining' },
      { id: 'record-result', label: '记录成绩', icon: '📊', iconClass: 'action-icon-record', action: 'recordResult' },
      { id: 'view-reports', label: '查看报告', icon: '📈', iconClass: 'action-icon-report', action: 'viewReports' }
    ]

    this.setData({ quickActions })
  },

  // 导航到页面
  navigateToPage(e) {
    const { url, auth } = e.currentTarget.dataset

    if (auth) {
      const app = getApp()
      if (!app.isLoggedIn()) {
        this.goToLogin()
        return
      }
    }

    wx.navigateTo({
      url: url,
      fail: () => {
        wx.switchTab({ url })
      }
    })
  },

  // 跳转到登录页
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 处理快捷操作
  handleQuickAction(e) {
    const { action } = e.currentTarget.dataset

    switch (action) {
      case 'addAthlete':
        wx.navigateTo({ url: '/pages/athlete/edit/edit' })
        break
      case 'createTraining':
        wx.navigateTo({ url: '/pages/training/plan/plan' })
        break
      case 'recordResult':
        wx.navigateTo({ url: '/pages/training/record/record' })
        break
      case 'viewReports':
        wx.navigateTo({ url: '/pages/training/analysis/analysis' })
        break
      default:
        wx.showToast({ title: '功能开发中', icon: 'none' })
    }
  },

  // 显示错误信息
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }
})

  // 加载页面数据
  async loadPageData() {
    const app = getApp()
    if (!app.isLoggedIn()) {
      return
    }

    try {
      this.setData({ loading: true })

      // 并行加载数据
      await Promise.all([
        this.loadStatsData(),
        this.loadRecentActivities(),
        this.loadTodayTraining(),
        this.loadNotices()
      ])

    } catch (error) {
      console.error('Load page data failed:', error)
      this.showError('数据加载失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 设置问候语
  setGreeting() {
    const hour = new Date().getHours()
    let greeting = ''

    if (hour < 6) {
      greeting = '夜深了'
    } else if (hour < 9) {
      greeting = '早上好'
    } else if (hour < 12) {
      greeting = '上午好'
    } else if (hour < 14) {
      greeting = '中午好'
    } else if (hour < 18) {
      greeting = '下午好'
    } else if (hour < 22) {
      greeting = '晚上好'
    } else {
      greeting = '夜深了'
    }

    this.setData({ greeting })
  },

  // 获取角色文本
  getRoleText(role) {
    const roleMap = {
      admin: '管理员',
      coach: '教练',
      athlete: '运动员'
    }
    return roleMap[role] || '用户'
  },

  // 初始化菜单项
  initMenuItems() {
    const app = getApp()
    const userRole = app.getUserRole()

    const allMenuItems = [
      {
        id: 'athletes',
        label: '运动员管理',
        icon: 'icon-athlete',
        iconClass: 'menu-icon-athlete',
        url: '/pages/athlete/list/list',
        requireAuth: true,
        roles: ['admin', 'coach']
      },
      {
        id: 'training',
        label: '训练管理',
        icon: 'icon-training',
        iconClass: 'menu-icon-training',
        url: '/pages/training/plan/plan',
        requireAuth: true,
        roles: ['admin', 'coach', 'athlete']
      },
      {
        id: 'competition',
        label: '比赛管理',
        icon: 'icon-competition',
        iconClass: 'menu-icon-competition',
        url: '/pages/competition/list/list',
        requireAuth: true,
        roles: ['admin', 'coach', 'athlete']
      },
      {
        id: 'analysis',
        label: '数据分析',
        icon: 'icon-chart',
        iconClass: 'menu-icon-analysis',
        url: '/pages/training/analysis/analysis',
        requireAuth: true,
        roles: ['admin', 'coach']
      },
      {
        id: 'profile',
        label: '个人中心',
        icon: 'icon-profile',
        iconClass: 'menu-icon-profile',
        url: '/pages/profile/profile',
        requireAuth: true,
        roles: ['admin', 'coach', 'athlete']
      }
    ]

    // 根据用户角色过滤菜单
    const menuItems = allMenuItems.filter(item => {
      if (!item.roles) return true
      return item.roles.includes(userRole)
    })

    this.setData({ menuItems })
  },

  // 初始化快捷操作
  initQuickActions() {
    const app = getApp()
    const userRole = app.getUserRole()

    let quickActions = []

    if (userRole === 'coach' || userRole === 'admin') {
      quickActions = [
        {
          id: 'add-athlete',
          label: '添加运动员',
          icon: 'icon-add-user',
          iconClass: 'action-icon-add',
          action: 'addAthlete'
        },
        {
          id: 'create-training',
          label: '创建训练',
          icon: 'icon-add-training',
          iconClass: 'action-icon-training',
          action: 'createTraining'
        },
        {
          id: 'record-result',
          label: '记录成绩',
          icon: 'icon-record',
          iconClass: 'action-icon-record',
          action: 'recordResult'
        },
        {
          id: 'view-reports',
          label: '查看报告',
          icon: 'icon-report',
          iconClass: 'action-icon-report',
          action: 'viewReports'
        }
      ]
    } else if (userRole === 'athlete') {
      quickActions = [
        {
          id: 'my-training',
          label: '我的训练',
          icon: 'icon-training',
          iconClass: 'action-icon-training',
          action: 'myTraining'
        },
        {
          id: 'my-results',
          label: '我的成绩',
          icon: 'icon-result',
          iconClass: 'action-icon-result',
          action: 'myResults'
        },
        {
          id: 'training-plan',
          label: '训练计划',
          icon: 'icon-plan',
          iconClass: 'action-icon-plan',
          action: 'trainingPlan'
        },
        {
          id: 'health-data',
          label: '健康数据',
          icon: 'icon-health',
          iconClass: 'action-icon-health',
          action: 'healthData'
        }
      ]
    }

    this.setData({ quickActions })
  }