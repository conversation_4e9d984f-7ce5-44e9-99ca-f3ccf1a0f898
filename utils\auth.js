// utils/auth.js
import { authAPI } from './api.js'
import { storage } from './storage.js'

class AuthService {
  constructor() {
    this.loginPromise = null
  }

  // 微信登录
  async wxLogin() {
    try {
      // 防止重复登录
      if (this.loginPromise) {
        return await this.loginPromise
      }

      this.loginPromise = this._performWxLogin()
      const result = await this.loginPromise
      this.loginPromise = null
      return result
    } catch (error) {
      this.loginPromise = null
      throw error
    }
  }

  // 执行微信登录
  async _performWxLogin() {
    try {
      // 1. 获取微信登录code
      const loginRes = await this._wxLogin()
      console.log('WeChat login code:', loginRes.code)

      // 2. 调用后端登录接口
      const authRes = await authAPI.wxLogin(loginRes.code)
      
      if (authRes.code !== 200) {
        throw new Error(authRes.message || '登录失败')
      }

      const { userInfo, token, role } = authRes.data

      // 3. 保存登录信息
      const loginInfo = {
        userInfo: {
          ...userInfo,
          token
        },
        userRole: role,
        token,
        loginTime: Date.now()
      }

      await storage.set('loginInfo', loginInfo)

      // 4. 更新全局状态
      const app = getApp()
      await app.login(loginInfo.userInfo, loginInfo.userRole)

      console.log('Login successful:', userInfo)
      return {
        success: true,
        userInfo: loginInfo.userInfo,
        userRole: loginInfo.userRole
      }
    } catch (error) {
      console.error('WeChat login failed:', error)
      throw new Error(error.message || '登录失败，请重试')
    }
  }

  // 微信登录获取code
  _wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: (error) => {
          console.error('wx.login failed:', error)
          reject(new Error('微信登录失败'))
        }
      })
    })
  }

  // 获取用户信息（需要用户授权）
  async getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('Get user profile success:', res)
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('Get user profile failed:', error)
          reject(new Error('获取用户信息失败'))
        }
      })
    })
  }

  // 检查登录状态
  async checkLoginStatus() {
    try {
      const loginInfo = await storage.get('loginInfo')
      
      if (!loginInfo || !loginInfo.token) {
        return { isLoggedIn: false }
      }

      // 检查token是否过期（7天）
      const now = Date.now()
      const loginTime = loginInfo.loginTime || 0
      const expireTime = 7 * 24 * 60 * 60 * 1000 // 7天
      
      if (now - loginTime > expireTime) {
        console.log('Token expired, need re-login')
        await this.logout()
        return { isLoggedIn: false }
      }

      // 验证token有效性
      const isValid = await this.validateToken(loginInfo.token)
      
      if (!isValid) {
        console.log('Token invalid, need re-login')
        await this.logout()
        return { isLoggedIn: false }
      }

      return {
        isLoggedIn: true,
        userInfo: loginInfo.userInfo,
        userRole: loginInfo.userRole
      }
    } catch (error) {
      console.error('Check login status failed:', error)
      return { isLoggedIn: false }
    }
  }

  // 验证token
  async validateToken(token) {
    try {
      const res = await authAPI.validateToken(token)
      return res.code === 200 && res.data.valid
    } catch (error) {
      console.error('Validate token failed:', error)
      return false
    }
  }

  // 更新用户信息
  async updateUserInfo(userInfo) {
    try {
      const res = await authAPI.updateUserInfo(userInfo)
      
      if (res.code === 200) {
        // 更新本地存储
        const loginInfo = await storage.get('loginInfo')
        if (loginInfo) {
          loginInfo.userInfo = { ...loginInfo.userInfo, ...userInfo }
          await storage.set('loginInfo', loginInfo)
          
          // 更新全局状态
          const app = getApp()
          app.globalData.userInfo = loginInfo.userInfo
        }
        
        return { success: true, data: res.data }
      } else {
        throw new Error(res.message || '更新用户信息失败')
      }
    } catch (error) {
      console.error('Update user info failed:', error)
      throw error
    }
  }

  // 登出
  async logout() {
    try {
      // 调用后端登出接口
      try {
        await authAPI.logout()
      } catch (error) {
        console.error('Backend logout failed:', error)
        // 即使后端登出失败，也要清除本地数据
      }

      // 清除本地登录信息
      await storage.remove('loginInfo')
      
      // 清除其他相关缓存
      await storage.remove('userPreferences')
      await storage.remove('pendingRequests')
      
      // 更新全局状态
      const app = getApp()
      app.globalData.userInfo = null
      app.globalData.userRole = null
      app.globalData.isLoggedIn = false

      console.log('Logout successful')
      return { success: true }
    } catch (error) {
      console.error('Logout failed:', error)
      throw error
    }
  }

  // 检查权限
  hasPermission(permission, userRole = null) {
    const app = getApp()
    const role = userRole || app.getUserRole()
    
    if (!role) {
      return false
    }

    const permissions = {
      admin: [
        'athlete.create', 'athlete.read', 'athlete.update', 'athlete.delete',
        'training.create', 'training.read', 'training.update', 'training.delete',
        'competition.create', 'competition.read', 'competition.update', 'competition.delete',
        'user.manage', 'system.config'
      ],
      coach: [
        'athlete.create', 'athlete.read', 'athlete.update',
        'training.create', 'training.read', 'training.update',
        'competition.read', 'competition.update'
      ],
      athlete: [
        'athlete.read', 'training.read', 'competition.read',
        'profile.update'
      ]
    }

    return permissions[role] && permissions[role].includes(permission)
  }

  // 检查是否为管理员
  isAdmin(userRole = null) {
    const app = getApp()
    const role = userRole || app.getUserRole()
    return role === 'admin'
  }

  // 检查是否为教练
  isCoach(userRole = null) {
    const app = getApp()
    const role = userRole || app.getUserRole()
    return role === 'coach'
  }

  // 检查是否为运动员
  isAthlete(userRole = null) {
    const app = getApp()
    const role = userRole || app.getUserRole()
    return role === 'athlete'
  }

  // 获取用户角色显示名称
  getRoleDisplayName(role = null) {
    const app = getApp()
    const userRole = role || app.getUserRole()
    
    const roleNames = {
      admin: '管理员',
      coach: '教练',
      athlete: '运动员'
    }
    
    return roleNames[userRole] || '未知角色'
  }

  // 权限守卫 - 页面级别
  async requireAuth(requiredRole = null) {
    const app = getApp()
    
    if (!app.isLoggedIn()) {
      // 未登录，跳转到登录页
      wx.redirectTo({
        url: '/pages/login/login'
      })
      return false
    }

    if (requiredRole) {
      const userRole = app.getUserRole()
      const roleLevel = { athlete: 1, coach: 2, admin: 3 }
      
      if (roleLevel[userRole] < roleLevel[requiredRole]) {
        // 权限不足
        wx.showModal({
          title: '权限不足',
          content: '您没有访问此页面的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return false
      }
    }

    return true
  }

  // 权限守卫 - 功能级别
  requirePermission(permission) {
    if (!this.hasPermission(permission)) {
      wx.showToast({
        title: '权限不足',
        icon: 'none'
      })
      return false
    }
    return true
  }
}

// 创建认证服务实例
export const auth = new AuthService()

// 页面权限装饰器
export function requireAuth(requiredRole = null) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      const hasAuth = await auth.requireAuth(requiredRole)
      if (hasAuth) {
        return originalMethod.apply(this, args)
      }
    }
    
    return descriptor
  }
}

// 功能权限装饰器
export function requirePermission(permission) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      if (auth.requirePermission(permission)) {
        return originalMethod.apply(this, args)
      }
    }
    
    return descriptor
  }
}
