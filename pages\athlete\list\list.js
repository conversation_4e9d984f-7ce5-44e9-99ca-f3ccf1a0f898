// pages/athlete/list/list.js
Page({
  data: {
    athleteList: [],
    searchKeyword: '',
    totalCount: 0,
    loading: false
  },

  onLoad(options) {
    console.log('Athlete list page loaded')
    this.loadAthleteList()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAthleteList()
  },

  onPullDownRefresh() {
    this.loadAthleteList().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载运动员列表
  async loadAthleteList() {
    try {
      this.setData({ loading: true })

      // 模拟数据
      const mockData = [
        {
          id: 1,
          name: '张三',
          specialty: '短跑',
          level: '高级',
          avatar: '/images/default-avatar.png',
          age: 20,
          gender: '男'
        },
        {
          id: 2,
          name: '李四',
          specialty: '中长跑',
          level: '中级',
          avatar: '/images/default-avatar.png',
          age: 22,
          gender: '女'
        },
        {
          id: 3,
          name: '王五',
          specialty: '跳跃',
          level: '初级',
          avatar: '/images/default-avatar.png',
          age: 19,
          gender: '男'
        },
        {
          id: 4,
          name: '赵六',
          specialty: '投掷',
          level: '专业',
          avatar: '/images/default-avatar.png',
          age: 24,
          gender: '女'
        }
      ]

      // 根据搜索关键词过滤
      let filteredData = mockData
      if (this.data.searchKeyword) {
        filteredData = mockData.filter(item =>
          item.name.includes(this.data.searchKeyword) ||
          item.specialty.includes(this.data.searchKeyword)
        )
      }

      this.setData({
        athleteList: filteredData,
        totalCount: filteredData.length
      })

    } catch (error) {
      console.error('Load athlete list failed:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })

    // 防抖搜索
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.loadAthleteList()
    }, 500)
  },

  // 清除搜索
  clearSearch() {
    this.setData({ searchKeyword: '' })
    this.loadAthleteList()
  },

  // 查看运动员详情
  viewAthleteDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/athlete/detail/detail?id=${id}`
    })
  },

  // 添加运动员
  addAthlete() {
    wx.navigateTo({
      url: '/pages/athlete/edit/edit'
    })
  }
})