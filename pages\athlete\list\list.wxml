<!--pages/athlete/list/list.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <text class="search-icon">🔍</text>
      <input class="search-input" placeholder="搜索运动员姓名或项目" value="{{searchKeyword}}" bindinput="onSearchInput" />
      <text class="clear-icon" wx:if="{{searchKeyword}}" bindtap="clearSearch">✕</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-bar">
    <text class="stats-text">共 {{totalCount}} 名运动员</text>
  </view>

  <!-- 运动员列表 -->
  <scroll-view class="athlete-list" scroll-y>
    <view class="athlete-item" wx:for="{{athleteList}}" wx:key="id" bindtap="viewAthleteDetail" data-id="{{item.id}}">
      <image class="athlete-avatar" src="{{item.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="athlete-info">
        <text class="athlete-name">{{item.name}}</text>
        <text class="athlete-specialty">{{item.specialty}}</text>
        <text class="athlete-level">{{item.level}}</text>
      </view>
      <text class="arrow-icon">›</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{athleteList.length === 0 && !loading}}">
      <text class="empty-icon">👥</text>
      <text class="empty-text">暂无运动员数据</text>
      <text class="empty-desc">点击右下角按钮添加运动员</text>
    </view>
  </scroll-view>

  <!-- 添加按钮 -->
  <view class="fab" bindtap="addAthlete">
    <text class="fab-icon">+</text>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>