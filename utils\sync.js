// utils/sync.js
import { api } from './api.js'
import { storage } from './storage.js'

class SyncService {
  constructor() {
    this.syncQueue = []
    this.isSyncing = false
    this.syncInterval = null
    this.retryCount = 3
    this.retryDelay = 1000
  }

  // 初始化同步服务
  async init() {
    try {
      // 加载待同步队列
      await this.loadSyncQueue()
      
      // 启动定时同步
      this.startAutoSync()
      
      console.log('Sync service initialized')
    } catch (error) {
      console.error('Sync service init failed:', error)
    }
  }

  // 加载待同步队列
  async loadSyncQueue() {
    try {
      const pendingRequests = await storage.get('pendingRequests', [])
      this.syncQueue = pendingRequests
      console.log(`Loaded ${this.syncQueue.length} pending requests`)
    } catch (error) {
      console.error('Load sync queue failed:', error)
      this.syncQueue = []
    }
  }

  // 保存同步队列
  async saveSyncQueue() {
    try {
      await storage.set('pendingRequests', this.syncQueue)
    } catch (error) {
      console.error('Save sync queue failed:', error)
    }
  }

  // 添加到同步队列
  async addToSyncQueue(request) {
    try {
      const syncItem = {
        id: this.generateSyncId(),
        ...request,
        timestamp: Date.now(),
        retryCount: 0,
        status: 'pending'
      }
      
      this.syncQueue.push(syncItem)
      await this.saveSyncQueue()
      
      console.log('Added to sync queue:', syncItem.id)
      
      // 如果网络可用，立即尝试同步
      const app = getApp()
      if (app.isOnline() && !this.isSyncing) {
        this.syncPendingData()
      }
    } catch (error) {
      console.error('Add to sync queue failed:', error)
    }
  }

  // 同步待处理数据
  async syncPendingData() {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return
    }

    const app = getApp()
    if (!app.isOnline()) {
      console.log('Network not available, skip sync')
      return
    }

    this.isSyncing = true
    console.log(`Starting sync, ${this.syncQueue.length} items in queue`)

    try {
      const successItems = []
      const failedItems = []

      for (const item of this.syncQueue) {
        try {
          await this.syncSingleItem(item)
          successItems.push(item)
          console.log(`Sync success: ${item.id}`)
        } catch (error) {
          console.error(`Sync failed: ${item.id}`, error)
          
          item.retryCount++
          item.lastError = error.message
          item.lastRetryTime = Date.now()
          
          if (item.retryCount >= this.retryCount) {
            item.status = 'failed'
            failedItems.push(item)
            console.log(`Sync permanently failed: ${item.id}`)
          } else {
            item.status = 'retry'
            failedItems.push(item)
          }
        }
      }

      // 更新同步队列
      this.syncQueue = failedItems
      await this.saveSyncQueue()

      console.log(`Sync completed: ${successItems.length} success, ${failedItems.length} failed`)

      // 通知同步结果
      if (successItems.length > 0) {
        this.notifySyncSuccess(successItems.length)
      }
      
      if (failedItems.length > 0) {
        this.notifySyncFailure(failedItems.length)
      }

    } catch (error) {
      console.error('Sync process failed:', error)
    } finally {
      this.isSyncing = false
    }
  }

  // 同步单个项目
  async syncSingleItem(item) {
    try {
      const { method, url, data, header } = item
      
      let response
      switch (method.toLowerCase()) {
        case 'post':
          response = await api.post(url, data, { header })
          break
        case 'put':
          response = await api.put(url, data, { header })
          break
        case 'delete':
          response = await api.delete(url, { header })
          break
        default:
          throw new Error(`Unsupported sync method: ${method}`)
      }

      // 处理同步成功后的回调
      if (item.onSuccess) {
        try {
          await item.onSuccess(response)
        } catch (callbackError) {
          console.error('Sync success callback failed:', callbackError)
        }
      }

      return response
    } catch (error) {
      // 处理同步失败后的回调
      if (item.onError) {
        try {
          await item.onError(error)
        } catch (callbackError) {
          console.error('Sync error callback failed:', callbackError)
        }
      }
      
      throw error
    }
  }

  // 启动自动同步
  startAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }

    // 每30秒检查一次同步
    this.syncInterval = setInterval(() => {
      this.syncPendingData()
    }, 30000)
  }

  // 停止自动同步
  stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
    }
  }

  // 手动触发同步
  async manualSync() {
    try {
      await this.syncPendingData()
      return {
        success: true,
        message: '同步完成'
      }
    } catch (error) {
      console.error('Manual sync failed:', error)
      return {
        success: false,
        message: '同步失败：' + error.message
      }
    }
  }

  // 清空同步队列
  async clearSyncQueue() {
    try {
      this.syncQueue = []
      await this.saveSyncQueue()
      console.log('Sync queue cleared')
    } catch (error) {
      console.error('Clear sync queue failed:', error)
    }
  }

  // 获取同步状态
  getSyncStatus() {
    return {
      isSyncing: this.isSyncing,
      queueLength: this.syncQueue.length,
      pendingItems: this.syncQueue.filter(item => item.status === 'pending').length,
      retryItems: this.syncQueue.filter(item => item.status === 'retry').length,
      failedItems: this.syncQueue.filter(item => item.status === 'failed').length
    }
  }

  // 获取同步队列详情
  getSyncQueueDetails() {
    return this.syncQueue.map(item => ({
      id: item.id,
      method: item.method,
      url: item.url,
      timestamp: item.timestamp,
      retryCount: item.retryCount,
      status: item.status,
      lastError: item.lastError,
      lastRetryTime: item.lastRetryTime
    }))
  }

  // 删除同步项目
  async removeSyncItem(itemId) {
    try {
      this.syncQueue = this.syncQueue.filter(item => item.id !== itemId)
      await this.saveSyncQueue()
      console.log(`Removed sync item: ${itemId}`)
    } catch (error) {
      console.error('Remove sync item failed:', error)
    }
  }

  // 重试失败的同步项目
  async retrySyncItem(itemId) {
    try {
      const item = this.syncQueue.find(item => item.id === itemId)
      if (item) {
        item.retryCount = 0
        item.status = 'pending'
        item.lastError = null
        item.lastRetryTime = null
        
        await this.saveSyncQueue()
        
        // 立即尝试同步
        if (!this.isSyncing) {
          this.syncPendingData()
        }
        
        console.log(`Retry sync item: ${itemId}`)
      }
    } catch (error) {
      console.error('Retry sync item failed:', error)
    }
  }

  // 生成同步ID
  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 通知同步成功
  notifySyncSuccess(count) {
    wx.showToast({
      title: `成功同步${count}条数据`,
      icon: 'success',
      duration: 2000
    })
  }

  // 通知同步失败
  notifySyncFailure(count) {
    console.log(`${count}条数据同步失败，将稍后重试`)
  }

  // 数据冲突解决
  async resolveConflict(localData, serverData, conflictType) {
    try {
      switch (conflictType) {
        case 'timestamp':
          // 基于时间戳的冲突解决
          return localData.updatedAt > serverData.updatedAt ? localData : serverData
          
        case 'version':
          // 基于版本号的冲突解决
          return localData.version > serverData.version ? localData : serverData
          
        case 'user_choice':
          // 用户选择解决冲突
          return await this.showConflictDialog(localData, serverData)
          
        case 'merge':
          // 合并数据
          return { ...serverData, ...localData }
          
        default:
          // 默认使用服务器数据
          return serverData
      }
    } catch (error) {
      console.error('Resolve conflict failed:', error)
      return serverData
    }
  }

  // 显示冲突解决对话框
  showConflictDialog(localData, serverData) {
    return new Promise((resolve) => {
      wx.showModal({
        title: '数据冲突',
        content: '本地数据与服务器数据不一致，请选择要保留的版本',
        confirmText: '保留本地',
        cancelText: '使用服务器',
        success: (res) => {
          resolve(res.confirm ? localData : serverData)
        },
        fail: () => {
          resolve(serverData)
        }
      })
    })
  }

  // 批量同步
  async batchSync(items) {
    try {
      const batchSize = 10 // 每批处理10个项目
      const batches = []
      
      for (let i = 0; i < items.length; i += batchSize) {
        batches.push(items.slice(i, i + batchSize))
      }
      
      const results = []
      for (const batch of batches) {
        const batchResults = await Promise.allSettled(
          batch.map(item => this.syncSingleItem(item))
        )
        results.push(...batchResults)
      }
      
      return results
    } catch (error) {
      console.error('Batch sync failed:', error)
      throw error
    }
  }

  // 增量同步
  async incrementalSync(lastSyncTime) {
    try {
      const response = await api.get('/sync/incremental', {
        lastSyncTime: lastSyncTime || 0
      })
      
      if (response.code === 200) {
        const { data } = response
        
        // 处理增量数据
        await this.processIncrementalData(data)
        
        // 更新最后同步时间
        await storage.set('lastSyncTime', Date.now())
        
        console.log('Incremental sync completed')
        return data
      } else {
        throw new Error(response.message || 'Incremental sync failed')
      }
    } catch (error) {
      console.error('Incremental sync failed:', error)
      throw error
    }
  }

  // 处理增量数据
  async processIncrementalData(data) {
    try {
      const { athletes, trainings, competitions } = data
      
      // 处理运动员数据
      if (athletes && athletes.length > 0) {
        await this.updateLocalData('athletes', athletes)
      }
      
      // 处理训练数据
      if (trainings && trainings.length > 0) {
        await this.updateLocalData('trainings', trainings)
      }
      
      // 处理比赛数据
      if (competitions && competitions.length > 0) {
        await this.updateLocalData('competitions', competitions)
      }
      
      console.log('Incremental data processed')
    } catch (error) {
      console.error('Process incremental data failed:', error)
    }
  }

  // 更新本地数据
  async updateLocalData(dataType, items) {
    try {
      const existingData = await storage.get(dataType, [])
      const updatedData = [...existingData]
      
      for (const item of items) {
        const existingIndex = updatedData.findIndex(existing => existing.id === item.id)
        
        if (existingIndex >= 0) {
          // 更新现有数据
          updatedData[existingIndex] = item
        } else {
          // 添加新数据
          updatedData.push(item)
        }
      }
      
      await storage.set(dataType, updatedData)
      console.log(`Updated ${dataType}: ${items.length} items`)
    } catch (error) {
      console.error(`Update local data failed for ${dataType}:`, error)
    }
  }
}

// 创建同步服务实例
export const sync = new SyncService()
