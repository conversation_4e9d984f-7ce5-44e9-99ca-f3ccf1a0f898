/* pages/login/login.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  left: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  right: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 登录内容 */
.login-content {
  position: relative;
  z-index: 1;
  padding: 80rpx 40rpx 40rpx;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
  backdrop-filter: blur(10rpx);
}

.logo-icon {
  font-size: 60rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 50rpx 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(10rpx);
}

.form-title {
  text-align: center;
  margin-bottom: 50rpx;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle-text {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 角色选择 */
.role-selection {
  margin-bottom: 50rpx;
}

.role-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.role-item:last-child {
  margin-bottom: 0;
}

.role-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.02);
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
}

.role-item:active {
  transform: scale(0.98);
}

.role-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 40rpx;
}

.role-item.active .role-icon {
  background: rgba(255, 255, 255, 0.2);
}

.role-content {
  flex: 1;
}

.role-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.role-item.active .role-name {
  color: white;
}

.role-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.role-item.active .role-desc {
  color: rgba(255, 255, 255, 0.8);
}

/* 登录按钮 */
.login-actions {
  text-align: center;
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 0;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-btn:disabled {
  opacity: 0.6;
  transform: none;
}

.login-btn:not(:disabled):active {
  transform: scale(0.98);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 28rpx;
}

.btn-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.loading-spinner {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn-text {
  font-size: 32rpx;
}

.login-tips {
  text-align: center;
  line-height: 1.6;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
}

.link-text {
  font-size: 24rpx;
  color: #667eea;
  text-decoration: underline;
}

/* 功能预览 */
.feature-preview {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(10rpx);
}

.preview-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.feature-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  font-size: 28rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  padding: 0 40rpx 40rpx;
}

.version-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.copyright-text {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 协议弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999;
  background: #f8f9fa;
  border-radius: 50%;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  max-height: 500rpx;
}

.modal-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-line;
}

.modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: bold;
}
