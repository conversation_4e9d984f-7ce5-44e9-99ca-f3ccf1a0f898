/**app.wxss**/
@import './styles/common.wxss';
@import './styles/theme.wxss';

/* 全局样式 */
page {
  background-color: #F5F7FA;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', <PERSON>l, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.content {
  padding: 0 20rpx;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #EBEEF5;
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #EBEEF5;
  background: #FAFBFC;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.btn-primary {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #fff;
}

.btn-primary:hover {
  opacity: 0.9;
}

.btn-secondary {
  background: #F4F4F5;
  color: #606266;
  border: 1rpx solid #DCDFE6;
}

.btn-success {
  background: #67C23A;
  color: #fff;
}

.btn-warning {
  background: #E6A23C;
  color: #fff;
}

.btn-danger {
  background: #F56C6C;
  color: #fff;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 28rpx 56rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #606266;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 24rpx;
  border: 1rpx solid #DCDFE6;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #606266;
  background: #fff;
  box-sizing: border-box;
}

.form-control:focus {
  border-color: #2E86AB;
  outline: none;
}

.form-control-error {
  border-color: #F56C6C;
}

.form-error {
  color: #F56C6C;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 列表样式 */
.list {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.list-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #EBEEF5;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background: #F5F7FA;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  color: #303133;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: #909399;
}

.list-item-action {
  margin-left: 20rpx;
}

/* 头像样式 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.tag-primary {
  background: #ECF5FF;
  color: #409EFF;
  border: 1rpx solid #B3D8FF;
}

.tag-success {
  background: #F0F9FF;
  color: #67C23A;
  border: 1rpx solid #C2E7B0;
}

.tag-warning {
  background: #FDF6EC;
  color: #E6A23C;
  border: 1rpx solid #F5DAB1;
}

.tag-danger {
  background: #FEF0F0;
  color: #F56C6C;
  border: 1rpx solid #FBC4C4;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  color: #909399;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E4E7ED;
  border-top: 4rpx solid #2E86AB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  color: #909399;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #C0C4CC;
  text-align: center;
  line-height: 1.5;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.margin-top { margin-top: 20rpx; }
.margin-bottom { margin-bottom: 20rpx; }
.margin-left { margin-left: 20rpx; }
.margin-right { margin-right: 20rpx; }

.padding { padding: 20rpx; }
.padding-top { padding-top: 20rpx; }
.padding-bottom { padding-bottom: 20rpx; }
.padding-left { padding-left: 20rpx; }
.padding-right { padding-right: 20rpx; }

.hidden { display: none; }
.visible { display: block; }

/* 响应式 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }
  
  .card-header,
  .card-body {
    padding: 20rpx;
  }
}
