// utils/api.js
import { storage } from './storage.js'

const BASE_URL = 'https://your-api-domain.com/api' // 替换为实际API地址
const TIMEOUT = 10000

class ApiService {
  constructor() {
    this.baseURL = BASE_URL
    this.timeout = TIMEOUT
    this.interceptors = {
      request: [],
      response: []
    }
  }

  // 添加请求拦截器
  addRequestInterceptor(interceptor) {
    this.interceptors.request.push(interceptor)
  }

  // 添加响应拦截器
  addResponseInterceptor(interceptor) {
    this.interceptors.response.push(interceptor)
  }

  // 基础请求方法
  async request(options) {
    const app = getApp()
    
    // 默认配置
    const config = {
      url: this.baseURL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: options.timeout || this.timeout
    }

    // 添加认证token
    const loginInfo = await storage.get('loginInfo')
    if (loginInfo && loginInfo.token) {
      config.header.Authorization = `Bearer ${loginInfo.token}`
    }

    // 执行请求拦截器
    for (const interceptor of this.interceptors.request) {
      try {
        await interceptor(config)
      } catch (error) {
        console.error('Request interceptor error:', error)
      }
    }

    // 检查网络状态
    if (!app.isOnline()) {
      // 离线模式，尝试从缓存获取数据
      const cacheKey = this.getCacheKey(config.url, config.data)
      const cachedData = await storage.get(cacheKey)
      if (cachedData) {
        console.log('Using cached data for:', config.url)
        return cachedData
      } else {
        throw new Error('网络连接不可用，且无缓存数据')
      }
    }

    return new Promise((resolve, reject) => {
      wx.request({
        ...config,
        success: async (res) => {
          try {
            // 执行响应拦截器
            for (const interceptor of this.interceptors.response) {
              try {
                await interceptor(res)
              } catch (error) {
                console.error('Response interceptor error:', error)
              }
            }

            if (res.statusCode >= 200 && res.statusCode < 300) {
              // 缓存GET请求的响应数据
              if (config.method === 'GET' && res.data.code === 200) {
                const cacheKey = this.getCacheKey(config.url, config.data)
                await storage.set(cacheKey, res.data, 5 * 60 * 1000) // 缓存5分钟
              }
              
              resolve(res.data)
            } else {
              this.handleError(res)
              reject(new Error(`HTTP ${res.statusCode}: ${res.data.message || '请求失败'}`))
            }
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          console.error('Request failed:', error)
          
          // 网络错误时，保存请求到待同步队列
          if (config.method !== 'GET') {
            this.savePendingRequest(config)
          }
          
          reject(new Error('网络请求失败，请检查网络连接'))
        }
      })
    })
  }

  // GET请求
  get(url, params = {}, options = {}) {
    const queryString = this.buildQueryString(params)
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    return this.request({
      url: fullUrl,
      method: 'GET',
      ...options
    })
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  // DELETE请求
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    })
  }

  // 文件上传
  uploadFile(url, filePath, formData = {}, options = {}) {
    const app = getApp()
    const config = {
      url: this.baseURL + url,
      filePath,
      name: options.name || 'file',
      formData,
      header: {
        ...options.header
      }
    }

    return new Promise(async (resolve, reject) => {
      // 添加认证token
      const loginInfo = await storage.get('loginInfo')
      if (loginInfo && loginInfo.token) {
        config.header.Authorization = `Bearer ${loginInfo.token}`
      }

      wx.uploadFile({
        ...config,
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(data)
            } else {
              reject(new Error(data.message || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应数据解析失败'))
          }
        },
        fail: (error) => {
          console.error('Upload failed:', error)
          reject(new Error('文件上传失败'))
        }
      })
    })
  }

  // 构建查询字符串
  buildQueryString(params) {
    const query = []
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        query.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      }
    }
    return query.join('&')
  }

  // 生成缓存键
  getCacheKey(url, data) {
    const key = url + JSON.stringify(data)
    return `api_cache_${this.hashCode(key)}`
  }

  // 简单哈希函数
  hashCode(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString()
  }

  // 保存待同步请求
  async savePendingRequest(config) {
    try {
      const pendingRequests = await storage.get('pendingRequests') || []
      pendingRequests.push({
        ...config,
        timestamp: Date.now()
      })
      await storage.set('pendingRequests', pendingRequests)
      console.log('Saved pending request:', config.url)
    } catch (error) {
      console.error('Save pending request failed:', error)
    }
  }

  // 处理错误响应
  handleError(res) {
    const app = getApp()
    
    switch (res.statusCode) {
      case 401:
        // 未授权，跳转到登录页
        app.logout()
        break
      case 403:
        app.showToast('权限不足')
        break
      case 404:
        app.showToast('请求的资源不存在')
        break
      case 500:
        app.showToast('服务器内部错误')
        break
      default:
        app.showToast(res.data.message || '请求失败')
    }
  }

  // 错误上报
  async reportError(errorData) {
    try {
      return await this.post('/system/error-report', errorData)
    } catch (error) {
      console.error('Report error failed:', error)
    }
  }
}

// 创建API实例
const api = new ApiService()

// 添加默认拦截器
api.addRequestInterceptor(async (config) => {
  console.log('Request:', config.method, config.url)
})

api.addResponseInterceptor(async (response) => {
  console.log('Response:', response.statusCode, response.data)
})

// 具体业务API
export const authAPI = {
  // 微信登录
  wxLogin: (code) => api.post('/auth/wx-login', { code }),
  
  // 获取用户信息
  getUserInfo: () => api.get('/auth/user-info'),
  
  // 更新用户信息
  updateUserInfo: (data) => api.put('/auth/user-info', data),
  
  // 验证token
  validateToken: (token) => api.post('/auth/validate-token', { token }),
  
  // 登出
  logout: () => api.post('/auth/logout')
}

export const athleteAPI = {
  // 获取运动员列表
  getAthletes: (params) => api.get('/athletes', params),
  
  // 获取运动员详情
  getAthlete: (id) => api.get(`/athletes/${id}`),
  
  // 创建运动员
  createAthlete: (data) => api.post('/athletes', data),
  
  // 更新运动员
  updateAthlete: (id, data) => api.put(`/athletes/${id}`, data),
  
  // 删除运动员
  deleteAthlete: (id) => api.delete(`/athletes/${id}`),
  
  // 上传运动员照片
  uploadPhoto: (filePath, athleteId) => api.uploadFile('/athletes/upload-photo', filePath, { athleteId }),
  
  // 搜索运动员
  searchAthletes: (keyword) => api.get('/athletes/search', { keyword }),
  
  // 批量导入运动员
  batchImport: (data) => api.post('/athletes/batch-import', data)
}

export const trainingAPI = {
  // 获取训练计划列表
  getTrainingPlans: (params) => api.get('/training/plans', params),
  
  // 获取训练计划详情
  getTrainingPlan: (id) => api.get(`/training/plans/${id}`),
  
  // 创建训练计划
  createTrainingPlan: (data) => api.post('/training/plans', data),
  
  // 更新训练计划
  updateTrainingPlan: (id, data) => api.put(`/training/plans/${id}`, data),
  
  // 删除训练计划
  deleteTrainingPlan: (id) => api.delete(`/training/plans/${id}`),
  
  // 获取训练记录
  getTrainingRecords: (params) => api.get('/training/records', params),
  
  // 创建训练记录
  createTrainingRecord: (data) => api.post('/training/records', data),
  
  // 获取训练分析数据
  getTrainingAnalysis: (params) => api.get('/training/analysis', params),
  
  // 获取AI推荐
  getAIRecommendations: (athleteId) => api.get(`/training/ai-recommendations/${athleteId}`)
}

export const competitionAPI = {
  // 获取比赛列表
  getCompetitions: (params) => api.get('/competitions', params),
  
  // 获取比赛详情
  getCompetition: (id) => api.get(`/competitions/${id}`),
  
  // 创建比赛
  createCompetition: (data) => api.post('/competitions', data),
  
  // 更新比赛
  updateCompetition: (id, data) => api.put(`/competitions/${id}`, data),
  
  // 删除比赛
  deleteCompetition: (id) => api.delete(`/competitions/${id}`),
  
  // 获取比赛成绩
  getCompetitionResults: (competitionId) => api.get(`/competitions/${competitionId}/results`),
  
  // 提交比赛成绩
  submitResult: (data) => api.post('/competitions/results', data)
}

export { api }
