// utils/storage.js

class StorageService {
  constructor() {
    this.prefix = 'track_field_'
    this.maxSize = 10 * 1024 * 1024 // 10MB
    this.initialized = false
  }

  // 初始化存储服务
  async init() {
    try {
      if (this.initialized) return

      // 检查存储空间
      await this.checkStorageSpace()
      
      // 清理过期数据
      await this.cleanExpiredData()
      
      this.initialized = true
      console.log('Storage service initialized')
    } catch (error) {
      console.error('Storage init failed:', error)
    }
  }

  // 生成完整的key
  getFullKey(key) {
    return `${this.prefix}${key}`
  }

  // 设置数据
  async set(key, value, expireTime = null) {
    try {
      const fullKey = this.getFullKey(key)
      const data = {
        value,
        timestamp: Date.now(),
        expireTime: expireTime ? Date.now() + expireTime : null
      }

      const jsonString = JSON.stringify(data)
      
      // 检查数据大小
      if (this.getStringSize(jsonString) > this.maxSize) {
        throw new Error('Data too large for storage')
      }

      return new Promise((resolve, reject) => {
        wx.setStorage({
          key: fullKey,
          data: jsonString,
          success: () => {
            console.log(`Storage set: ${key}`)
            resolve(true)
          },
          fail: (error) => {
            console.error(`Storage set failed: ${key}`, error)
            reject(error)
          }
        })
      })
    } catch (error) {
      console.error('Set storage failed:', error)
      throw error
    }
  }

  // 获取数据
  async get(key, defaultValue = null) {
    try {
      const fullKey = this.getFullKey(key)
      
      return new Promise((resolve) => {
        wx.getStorage({
          key: fullKey,
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              
              // 检查是否过期
              if (data.expireTime && Date.now() > data.expireTime) {
                console.log(`Storage expired: ${key}`)
                this.remove(key) // 异步删除过期数据
                resolve(defaultValue)
                return
              }
              
              console.log(`Storage get: ${key}`)
              resolve(data.value)
            } catch (parseError) {
              console.error(`Storage parse failed: ${key}`, parseError)
              resolve(defaultValue)
            }
          },
          fail: (error) => {
            console.log(`Storage get failed: ${key}`, error.errMsg)
            resolve(defaultValue)
          }
        })
      })
    } catch (error) {
      console.error('Get storage failed:', error)
      return defaultValue
    }
  }

  // 删除数据
  async remove(key) {
    try {
      const fullKey = this.getFullKey(key)
      
      return new Promise((resolve) => {
        wx.removeStorage({
          key: fullKey,
          success: () => {
            console.log(`Storage removed: ${key}`)
            resolve(true)
          },
          fail: (error) => {
            console.error(`Storage remove failed: ${key}`, error)
            resolve(false)
          }
        })
      })
    } catch (error) {
      console.error('Remove storage failed:', error)
      return false
    }
  }

  // 清空所有数据
  async clear() {
    try {
      return new Promise((resolve) => {
        wx.clearStorage({
          success: () => {
            console.log('Storage cleared')
            resolve(true)
          },
          fail: (error) => {
            console.error('Storage clear failed:', error)
            resolve(false)
          }
        })
      })
    } catch (error) {
      console.error('Clear storage failed:', error)
      return false
    }
  }

  // 获取所有key
  async getKeys() {
    try {
      return new Promise((resolve) => {
        wx.getStorageInfo({
          success: (res) => {
            const keys = res.keys.filter(key => key.startsWith(this.prefix))
              .map(key => key.replace(this.prefix, ''))
            resolve(keys)
          },
          fail: (error) => {
            console.error('Get storage keys failed:', error)
            resolve([])
          }
        })
      })
    } catch (error) {
      console.error('Get keys failed:', error)
      return []
    }
  }

  // 检查key是否存在
  async has(key) {
    try {
      const value = await this.get(key)
      return value !== null
    } catch (error) {
      return false
    }
  }

  // 获取存储信息
  async getStorageInfo() {
    try {
      return new Promise((resolve) => {
        wx.getStorageInfo({
          success: (res) => {
            resolve({
              keys: res.keys,
              currentSize: res.currentSize,
              limitSize: res.limitSize
            })
          },
          fail: (error) => {
            console.error('Get storage info failed:', error)
            resolve(null)
          }
        })
      })
    } catch (error) {
      console.error('Get storage info failed:', error)
      return null
    }
  }

  // 检查存储空间
  async checkStorageSpace() {
    try {
      const info = await this.getStorageInfo()
      if (info) {
        const usagePercent = (info.currentSize / info.limitSize) * 100
        console.log(`Storage usage: ${usagePercent.toFixed(2)}%`)
        
        if (usagePercent > 80) {
          console.warn('Storage usage is high, cleaning up...')
          await this.cleanup()
        }
      }
    } catch (error) {
      console.error('Check storage space failed:', error)
    }
  }

  // 清理过期数据
  async cleanExpiredData() {
    try {
      const keys = await this.getKeys()
      const now = Date.now()
      
      for (const key of keys) {
        try {
          const fullKey = this.getFullKey(key)
          const res = await this.getStorageSync(fullKey)
          
          if (res) {
            const data = JSON.parse(res)
            if (data.expireTime && now > data.expireTime) {
              await this.remove(key)
              console.log(`Cleaned expired data: ${key}`)
            }
          }
        } catch (error) {
          // 数据损坏，删除
          await this.remove(key)
          console.log(`Cleaned corrupted data: ${key}`)
        }
      }
    } catch (error) {
      console.error('Clean expired data failed:', error)
    }
  }

  // 同步获取存储数据
  getStorageSync(key) {
    try {
      return wx.getStorageSync(key)
    } catch (error) {
      console.error('Get storage sync failed:', error)
      return null
    }
  }

  // 清理存储空间
  async cleanup() {
    try {
      const keys = await this.getKeys()
      const dataList = []
      
      // 获取所有数据的时间戳
      for (const key of keys) {
        try {
          const fullKey = this.getFullKey(key)
          const res = await this.getStorageSync(fullKey)
          
          if (res) {
            const data = JSON.parse(res)
            dataList.push({
              key,
              timestamp: data.timestamp || 0,
              size: this.getStringSize(res)
            })
          }
        } catch (error) {
          // 数据损坏，标记删除
          dataList.push({
            key,
            timestamp: 0,
            size: 0
          })
        }
      }
      
      // 按时间戳排序，删除最旧的数据
      dataList.sort((a, b) => a.timestamp - b.timestamp)
      
      const deleteCount = Math.ceil(dataList.length * 0.2) // 删除20%的旧数据
      for (let i = 0; i < deleteCount; i++) {
        await this.remove(dataList[i].key)
        console.log(`Cleaned old data: ${dataList[i].key}`)
      }
      
      console.log(`Cleanup completed, removed ${deleteCount} items`)
    } catch (error) {
      console.error('Cleanup failed:', error)
    }
  }

  // 获取字符串大小（字节）
  getStringSize(str) {
    return new Blob([str]).size
  }

  // 批量设置
  async setBatch(data) {
    try {
      const promises = Object.entries(data).map(([key, value]) => {
        return this.set(key, value)
      })
      
      await Promise.all(promises)
      return true
    } catch (error) {
      console.error('Set batch failed:', error)
      return false
    }
  }

  // 批量获取
  async getBatch(keys) {
    try {
      const promises = keys.map(key => this.get(key))
      const values = await Promise.all(promises)
      
      const result = {}
      keys.forEach((key, index) => {
        result[key] = values[index]
      })
      
      return result
    } catch (error) {
      console.error('Get batch failed:', error)
      return {}
    }
  }

  // 批量删除
  async removeBatch(keys) {
    try {
      const promises = keys.map(key => this.remove(key))
      await Promise.all(promises)
      return true
    } catch (error) {
      console.error('Remove batch failed:', error)
      return false
    }
  }

  // 设置用户偏好
  async setUserPreference(key, value) {
    const preferences = await this.get('userPreferences', {})
    preferences[key] = value
    return await this.set('userPreferences', preferences)
  }

  // 获取用户偏好
  async getUserPreference(key, defaultValue = null) {
    const preferences = await this.get('userPreferences', {})
    return preferences[key] !== undefined ? preferences[key] : defaultValue
  }

  // 缓存API响应
  async cacheApiResponse(url, data, expireTime = 5 * 60 * 1000) {
    const cacheKey = `api_cache_${this.hashCode(url)}`
    return await this.set(cacheKey, data, expireTime)
  }

  // 获取缓存的API响应
  async getCachedApiResponse(url) {
    const cacheKey = `api_cache_${this.hashCode(url)}`
    return await this.get(cacheKey)
  }

  // 简单哈希函数
  hashCode(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString()
  }
}

// 创建存储服务实例
export const storage = new StorageService()
