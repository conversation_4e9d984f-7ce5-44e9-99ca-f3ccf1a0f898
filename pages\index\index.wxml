<!--pages/index/index.wxml-->
<view class="container">
  <!-- 顶部欢迎区域 -->
  <view class="welcome-section">
    <view class="welcome-bg">
      <view class="welcome-content">
        <view class="user-info" wx:if="{{userInfo}}">
          <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
          <view class="user-text">
            <text class="greeting">{{greeting}}</text>
            <text class="username">{{userInfo.nickName || '用户'}}</text>
            <text class="role">{{roleText}}</text>
          </view>
        </view>
        <view class="login-prompt" wx:else>
          <text class="prompt-text">欢迎使用田径训练管理系统</text>
          <button class="login-btn" bindtap="goToLogin">立即登录</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速统计 -->
  <view class="stats-section" wx:if="{{userInfo}}">
    <view class="stats-grid">
      <view class="stat-item" wx:for="{{statsData}}" wx:key="key">
        <view class="stat-icon {{item.iconClass}}">
          <text class="iconfont {{item.icon}}"></text>
        </view>
        <view class="stat-content">
          <text class="stat-number">{{item.value}}</text>
          <text class="stat-label">{{item.label}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="section-title">
      <text class="title-text">功能菜单</text>
    </view>
    <view class="menu-grid">
      <view class="menu-item" wx:for="{{menuItems}}" wx:key="id" bindtap="navigateToPage" data-url="{{item.url}}" data-auth="{{item.requireAuth}}">
        <view class="menu-icon {{item.iconClass}}">
          <text class="iconfont {{item.icon}}"></text>
        </view>
        <text class="menu-label">{{item.label}}</text>
        <view class="menu-badge" wx:if="{{item.badge}}">{{item.badge}}</view>
      </view>
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="activity-section" wx:if="{{userInfo && recentActivities.length > 0}}">
    <view class="section-title">
      <text class="title-text">最近活动</text>
      <text class="more-link" bindtap="viewAllActivities">查看全部</text>
    </view>
    <view class="activity-list">
      <view class="activity-item" wx:for="{{recentActivities}}" wx:key="id">
        <view class="activity-icon {{item.typeClass}}">
          <text class="iconfont {{item.icon}}"></text>
        </view>
        <view class="activity-content">
          <text class="activity-title">{{item.title}}</text>
          <text class="activity-desc">{{item.description}}</text>
          <text class="activity-time">{{item.timeText}}</text>
        </view>
        <view class="activity-action" wx:if="{{item.actionText}}" bindtap="handleActivityAction" data-action="{{item.action}}" data-id="{{item.id}}">
          <text class="action-text">{{item.actionText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日训练计划 -->
  <view class="training-section" wx:if="{{userInfo && todayTraining}}">
    <view class="section-title">
      <text class="title-text">今日训练</text>
      <text class="more-link" bindtap="viewTrainingPlan">查看计划</text>
    </view>
    <view class="training-card">
      <view class="training-header">
        <text class="training-title">{{todayTraining.title}}</text>
        <view class="training-status {{todayTraining.statusClass}}">
          <text class="status-text">{{todayTraining.statusText}}</text>
        </view>
      </view>
      <view class="training-content">
        <view class="training-info">
          <view class="info-item">
            <text class="info-label">训练时间</text>
            <text class="info-value">{{todayTraining.time}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">训练强度</text>
            <text class="info-value">{{todayTraining.intensity}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">预计时长</text>
            <text class="info-value">{{todayTraining.duration}}</text>
          </view>
        </view>
        <view class="training-actions">
          <button class="action-btn secondary" wx:if="{{todayTraining.canView}}" bindtap="viewTrainingDetail" data-id="{{todayTraining.id}}">查看详情</button>
          <button class="action-btn primary" wx:if="{{todayTraining.canStart}}" bindtap="startTraining" data-id="{{todayTraining.id}}">开始训练</button>
          <button class="action-btn success" wx:if="{{todayTraining.canRecord}}" bindtap="recordTraining" data-id="{{todayTraining.id}}">记录成绩</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions" wx:if="{{userInfo}}">
    <view class="section-title">
      <text class="title-text">快捷操作</text>
    </view>
    <view class="actions-grid">
      <view class="action-item" wx:for="{{quickActions}}" wx:key="id" bindtap="handleQuickAction" data-action="{{item.action}}">
        <view class="action-icon {{item.iconClass}}">
          <text class="iconfont {{item.icon}}"></text>
        </view>
        <text class="action-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>