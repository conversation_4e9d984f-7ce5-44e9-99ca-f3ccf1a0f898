<!--pages/login/login.wxml-->
<view class="container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="circle circle-1"></view>
    <view class="circle circle-2"></view>
    <view class="circle circle-3"></view>
  </view>

  <!-- 登录内容 -->
  <view class="login-content">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo">
        <text class="logo-icon">🏃‍♂️</text>
      </view>
      <text class="app-name">田径训练管理</text>
      <text class="app-desc">专业的田径训练管理平台</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-title">
        <text class="title-text">欢迎登录</text>
        <text class="subtitle-text">请选择您的身份进行登录</text>
      </view>

      <!-- 角色选择 -->
      <view class="role-selection">
        <view class="role-item {{selectedRole === 'athlete' ? 'active' : ''}}" bindtap="selectRole" data-role="athlete">
          <view class="role-icon">🏃</view>
          <text class="role-name">运动员</text>
          <text class="role-desc">查看训练计划和个人成绩</text>
        </view>
        <view class="role-item {{selectedRole === 'coach' ? 'active' : ''}}" bindtap="selectRole" data-role="coach">
          <view class="role-icon">👨‍🏫</view>
          <text class="role-name">教练</text>
          <text class="role-desc">管理运动员和训练计划</text>
        </view>
        <view class="role-item {{selectedRole === 'admin' ? 'active' : ''}}" bindtap="selectRole" data-role="admin">
          <view class="role-icon">👨‍💼</view>
          <text class="role-name">管理员</text>
          <text class="role-desc">系统管理和数据分析</text>
        </view>
      </view>

      <!-- 登录按钮 -->
      <view class="login-actions">
        <button class="login-btn" bindtap="handleWxLogin" disabled="{{!selectedRole || loading}}">
          <view class="btn-content">
            <text class="btn-icon" wx:if="{{!loading}}">📱</text>
            <view class="loading-spinner" wx:if="{{loading}}"></view>
            <text class="btn-text">{{loading ? '登录中...' : '微信一键登录'}}</text>
          </view>
        </button>

        <view class="login-tips">
          <text class="tips-text">登录即表示同意</text>
          <text class="link-text" bindtap="showPrivacyPolicy">《隐私政策》</text>
          <text class="tips-text">和</text>
          <text class="link-text" bindtap="showUserAgreement">《用户协议》</text>
        </view>
      </view>
    </view>

    <!-- 功能预览 -->
    <view class="feature-preview">
      <text class="preview-title">主要功能</text>
      <view class="feature-list">
        <view class="feature-item">
          <text class="feature-icon">📊</text>
          <text class="feature-text">训练数据分析</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📅</text>
          <text class="feature-text">训练计划管理</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🏆</text>
          <text class="feature-text">比赛成绩记录</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">👥</text>
          <text class="feature-text">团队协作管理</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer-info">
    <text class="version-text">版本 1.0.0</text>
    <text class="copyright-text">© 2024 田径训练管理系统</text>
  </view>
</view>

<!-- 协议弹窗 -->
<view class="modal-overlay" wx:if="{{showModal}}" bindtap="hideModal">
  <view class="modal-content" catchtap="preventClose">
    <view class="modal-header">
      <text class="modal-title">{{modalTitle}}</text>
      <text class="modal-close" bindtap="hideModal">✕</text>
    </view>
    <scroll-view class="modal-body" scroll-y>
      <text class="modal-text">{{modalContent}}</text>
    </scroll-view>
    <view class="modal-footer">
      <button class="modal-btn" bindtap="hideModal">我知道了</button>
    </view>
  </view>
</view>