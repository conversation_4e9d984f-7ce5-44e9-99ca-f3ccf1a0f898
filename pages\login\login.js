// pages/login/login.js
Page({
  data: {
    selectedRole: '',
    loading: false,
    showModal: false,
    modalTitle: '',
    modalContent: ''
  },

  onLoad(options) {
    console.log('Login page loaded')
    // 检查是否已经登录
    this.checkLoginStatus()
  },

  onShow() {
    // 重置状态
    this.setData({
      loading: false
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    if (app && app.isLoggedIn && app.isLoggedIn()) {
      // 已登录，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      })
    }
  },

  // 选择角色
  selectRole(e) {
    const { role } = e.currentTarget.dataset
    this.setData({
      selectedRole: role
    })

    wx.vibrateShort() // 触觉反馈
  },

  // 处理微信登录
  async handleWxLogin() {
    if (!this.data.selectedRole) {
      wx.showToast({
        title: '请先选择身份',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ loading: true })

      // 模拟登录过程
      await this.simulateLogin()

      // 登录成功
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转到首页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      console.error('Login failed:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 模拟登录过程
  async simulateLogin() {
    return new Promise((resolve, reject) => {
      // 模拟网络请求延迟
      setTimeout(() => {
        try {
          // 模拟用户信息
          const mockUserInfo = {
            nickName: this.getRoleDisplayName(this.data.selectedRole) + '用户',
            avatarUrl: '/images/default-avatar.png',
            openid: 'mock_openid_' + Date.now(),
            token: 'mock_token_' + Date.now()
          }

          // 保存到全局状态
          const app = getApp()
          if (app && app.login) {
            app.login(mockUserInfo, this.data.selectedRole)
          }

          resolve(mockUserInfo)
        } catch (error) {
          reject(error)
        }
      }, 2000)
    })
  },

  // 获取角色显示名称
  getRoleDisplayName(role) {
    const roleMap = {
      athlete: '运动员',
      coach: '教练',
      admin: '管理员'
    }
    return roleMap[role] || '用户'
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    this.setData({
      showModal: true,
      modalTitle: '隐私政策',
      modalContent: `
1. 信息收集
我们会收集您在使用服务时提供的信息，包括但不限于：
- 基本信息：姓名、联系方式等
- 训练数据：训练记录、成绩数据等
- 设备信息：设备型号、操作系统等

2. 信息使用
我们收集的信息将用于：
- 提供和改进服务
- 个性化推荐
- 数据分析和统计

3. 信息保护
我们采用行业标准的安全措施保护您的个人信息：
- 数据加密传输
- 访问权限控制
- 定期安全审计

4. 信息共享
除法律要求外，我们不会向第三方分享您的个人信息。

5. 联系我们
如有疑问，请联系我们：<EMAIL>
      `
    })
  },

  // 显示用户协议
  showUserAgreement() {
    this.setData({
      showModal: true,
      modalTitle: '用户协议',
      modalContent: `
1. 服务条款
欢迎使用田径训练管理系统。使用本服务即表示您同意遵守以下条款。

2. 用户责任
- 提供真实、准确的信息
- 合理使用服务，不得滥用
- 保护账户安全

3. 服务内容
- 训练计划管理
- 成绩记录和分析
- 团队协作功能

4. 知识产权
本服务的所有内容均受知识产权法保护。

5. 免责声明
在法律允许的范围内，我们不对服务中断、数据丢失等承担责任。

6. 协议修改
我们保留随时修改本协议的权利，修改后的协议将在应用内公布。

7. 法律适用
本协议受中华人民共和国法律管辖。
      `
    })
  },

  // 隐藏弹窗
  hideModal() {
    this.setData({
      showModal: false,
      modalTitle: '',
      modalContent: ''
    })
  },

  // 阻止弹窗关闭
  preventClose() {
    // 空函数，阻止事件冒泡
  }
})